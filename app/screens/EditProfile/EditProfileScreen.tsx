import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useState } from "react"
import { EditProfileContent } from "./components/EditProfileContent"
import type { UserProfile } from "./types"
import { AppStackScreenProps } from "@/navigators"
import { BottomSheetModalProvider } from "@gorhom/bottom-sheet"
import { SupabaseProfileService } from "@/services/supabase/profile-service"
import { useStores } from "@/models"
import { showErrorToast, showSuccessToast } from "@/utils/toast"

interface EditProfileScreenProps extends AppStackScreenProps<"EditProfile"> {}

export const EditProfileScreen: FC<EditProfileScreenProps> = observer(function EditProfileScreen({
  navigation,
  route,
}) {
  const { initialProfile } = route.params

  // Convert date string (if any) to Date object
  const processedInitialProfile = initialProfile ? {
    ...initialProfile,
    dateOfBirth:
      typeof initialProfile.dateOfBirth === "string"
        ? new Date(initialProfile.dateOfBirth)
        : initialProfile.dateOfBirth,
  } : undefined

  // Local state holding profile used to populate the form
  const [profile, setProfile] = useState<UserProfile | undefined>(processedInitialProfile)

  const { userProfileStore } = useStores()

  useEffect(() => {
    if (!processedInitialProfile) {
      SupabaseProfileService.fetchCurrentUserProfile()
        .then((data) => {
          if (data) setProfile(data)
          if (data) {
            userProfileStore.setProfile({
              userId: userProfileStore.userId,
              displayName: data.name,
              email: data.email,
              countryCode: data.countryCode,
              phone: data.phone,
              gender: data.gender,
              dateOfBirth: data.dateOfBirth,
              description: data.description,
              location: data.location,
            })
          }
        })
        .catch((e) => console.error("Failed to fetch profile:", e))
    }
  }, [])

  const handleClose = () => {
    navigation.goBack()
  }

  const handleSave = async (updatedProfile: UserProfile) => {
    try {
      await SupabaseProfileService.updateCurrentUserProfile(updatedProfile)
      // update cache store
      userProfileStore.setProfile({
        userId: userProfileStore.userId,
        displayName: updatedProfile.name,
        email: updatedProfile.email,
        countryCode: updatedProfile.countryCode,
        phone: updatedProfile.phone,
        gender: updatedProfile.gender,
        dateOfBirth: updatedProfile.dateOfBirth,
        description: updatedProfile.description,
        location: updatedProfile.location,
      })
      // Show success toast *before* closing the screen so it's visible
      showSuccessToast("Profile updated successfully")
      navigation.goBack()
    } catch (error) {
      showErrorToast("Failed to save profile. Please try again.")
    }
  }

  // Wrap with a local BottomSheetModalProvider because iOS modal presentations
  // render in a separate React tree that does not have access to the root-level
  // provider defined in `AppNavigator.tsx`. Placing the provider here ensures
  // the bottom sheet works consistently across platforms.
  return (
    <BottomSheetModalProvider>
      <EditProfileContent
        onClose={handleClose}
        onSave={handleSave}
        initialProfile={profile}
      />
    </BottomSheetModalProvider>
  )
})
