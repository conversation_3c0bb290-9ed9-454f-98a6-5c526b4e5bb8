import { FC } from "react"
import { 
  View, 
  ViewStyle, 
  ScrollView, 
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform
} from "react-native"
import { observer } from "mobx-react-lite"
import { Text, Icon, Button, Screen } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { useSafeAreaInsetsStyle } from "@/utils/useSafeAreaInsetsStyle"
import type { ThemedStyle } from "@/theme"
import type { EditProfileContentProps } from "../types"
import { useEditProfileForm, useEditProfileActions } from "../hooks"
import { ProfileAvatar } from "./ProfileAvatar"
import { FormFields } from "./FormFields"

export const EditProfileContent: FC<EditProfileContentProps> = observer(function EditProfileContent({
  onClose,
  onSave,
  initialProfile,
  loading = false,
}) {
  const { themed } = useAppTheme()
  const $bottomInset = useSafeAreaInsetsStyle(["bottom"])

  // Form management hook
  const {
    formData,
    isValid,
    errors,
    handleFieldChange,
    resetForm,
  } = useEditProfileForm({
    initialProfile,
    onSave,
  })

  // Actions management hook
  const {
    handleSave,
    handleCancel,
    handleImagePicker,
  } = useEditProfileActions({
    onClose,
    onSave,
    formData,
    isValid,
  })

  // Handle close with form reset
  const handleClose = () => {
    resetForm()
    handleCancel()
  }

  return (
    <Screen 
      preset="fixed" 
      safeAreaEdges={[]}
      contentContainerStyle={themed($container)}
    >
      <KeyboardAvoidingView
        style={themed($keyboardContainer)}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        {/* Header */}
        <View style={themed($header)}>
          <TouchableOpacity onPress={handleClose} style={themed($headerButton)}>
            <Icon icon="x" size={24} />
          </TouchableOpacity>
          <Text tx="editProfileScreen:editProfileTitle" style={themed($headerTitle)} size="lg" weight="semiBold" />
          <Button
            tx="common:save"
            onPress={handleSave}
            disabled={!isValid || loading}
            style={themed($saveButton)}
            preset="filled"
          />
        </View>

        {/* Content */}
        <ScrollView
          style={themed($scrollView)}
          contentContainerStyle={[themed($scrollContent), $bottomInset]}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Profile Avatar Section */}
          <ProfileAvatar
            name={formData.name}
            avatar={formData.avatar}
            onImagePress={handleImagePicker}
          />

          {/* Form Fields Section */}
          <FormFields
            formData={formData}
            errors={errors}
            onFieldChange={handleFieldChange}
          />
        </ScrollView>
      </KeyboardAvoidingView>
    </Screen>
  )
})

const $keyboardContainer: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $container: ThemedStyle<ViewStyle> = ({ colors }) => ({
  flex: 1,
  backgroundColor: colors.background,
})

const $header: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  paddingHorizontal: spacing.lg,
  paddingVertical: spacing.sm,
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral300,
  backgroundColor: colors.background,
})

const $headerButton: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  padding: spacing.xs,
  minWidth: 60,
})

const $headerTitle: ThemedStyle<ViewStyle> = ({ colors }) => ({
  color: colors.text,
  flex: 1,
  textAlign: "center",
})

const $saveButton: ThemedStyle<ViewStyle> = () => ({
  minWidth: 60,
  minHeight: 40,
  paddingHorizontal: 16,
  paddingVertical: 4,
})

const $scrollView: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $scrollContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingTop: spacing.lg,
  paddingBottom: spacing.xl,
}) 