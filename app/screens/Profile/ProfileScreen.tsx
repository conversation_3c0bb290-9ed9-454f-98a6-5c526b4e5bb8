import { observer } from "mobx-react-lite"
import { FC, useState } from "react"
import { View, ViewStyle, LayoutChangeEvent, Platform, RefreshControl } from "react-native"
import { Screen, Icon } from "@/components"
import { AppStackScreenProps } from "@/navigators"
import { $styles } from "@/theme"
import { getSafeAreaConfig } from "@/utils/safeAreaHelpers"
import { useAppTheme } from "@/utils/useAppTheme"
import { useHeader } from "@/utils/useHeader"
// import { useSafeAreaInsetsStyle } from "@/utils/useSafeAreaInsetsStyle"

import {
  useProfileData,
  useProfileActions,
  useProfileNavigation,
  useProfileUIActions,
  useProfileScrollToTop
} from "./hooks"
import { ProfileHeader, ProfileList, SettingsPopup } from "./components"
import { scrollManager, setCurrentUserForTesting } from "./utils"
import type { ThemedStyle } from "@/theme"
import { translate } from "@/i18n"

type ProfileScreenProps = AppStackScreenProps<"Profile"> | AppStackScreenProps<"MemberProfile">

export const ProfileScreen: FC<ProfileScreenProps> = observer(function ProfileScreen(props) {
  const navigation = props.navigation
  const route = props.route
  const userId = (route as any)?.params?.userId

  const {
    themed,
    theme: { colors },
    themeContext,
    setThemeContextOverride,
  } = useAppTheme()

  const [rightButtonsLayout, setRightButtonsLayout] = useState({ x: 0, y: 0, width: 0, height: 0 })

  const handleRightButtonsLayout = (event: LayoutChangeEvent) => {
    const { x, y, width, height } = event.nativeEvent.layout
    setRightButtonsLayout({ x, y, width, height })
  }

  // Data hook - only profile data
  const { profileData, isCurrentUser, isRefreshing, refreshProfileData } = useProfileData({ userId })

  // UI state hook - tab and popup state
  const {
    activeTab,
    settingsPopupVisible,
    handleTabChange,
    handleSettingsPopupOpen,
    handleSettingsPopupClose,
  } = useProfileUIActions({ isCurrentUser })

  // Navigation hook - all navigation actions
  const {
    handleBackPress,
    handleEventPress,
    handleEventDetailPress,
    handleCommunityPress,
  } = useProfileNavigation({ navigation, isCurrentUser })

  // Business actions hook - profile-specific business logic
  const {
    handleSharePress,
    handleSettingsPress: handleSettingsButton,
    handleCloseSettingsPopup,
    handleEditProfile,
    handleChangePassword,
    handleHelp,
    handleToggleLightMode,
  } = useProfileActions({
    profileData,
    onSettingsOpen: handleSettingsPopupOpen,
    onSettingsClose: handleSettingsPopupClose,
    onEditProfileOpen: () => {
      navigation.navigate("EditProfile", {})
    },
    onChangePasswordOpen: () => {
      navigation.navigate("ChangePassword")
    },
    setThemeContextOverride,
    themeContext,
  })

  // Scroll management hook
  const { scrollViewRef } = useProfileScrollToTop()

  useHeader({
    title: translate("profileScreen:title"),
    showBackButton: true,
    onBackPress: handleBackPress,
    rightButtons: [
      {
        icon: "share",
        onPress: handleSharePress,
      },
      ...(isCurrentUser
        ? [
            {
              icon: "settings" as const,
              onPress: handleSettingsButton,
            },
          ]
        : []),
    ],
    onRightButtonsLayout: handleRightButtonsLayout,
  })

  const handleMessagePress = () => {
    if (!isCurrentUser && navigation && profileData.profile) {
      // Convert profile ID to the expected participantId format
      const participantId = profileData.profile.id
      // Navigate to PrivateChat with proper typing
      navigation.navigate("PrivateChat", {
        participantId,
        participantName: profileData.profile.name,
      })
    } else {
      console.log("Cannot message current user or missing navigation/profile data")
    }
  }

  return (
    <Screen
      preset="fixed"
      safeAreaEdges={getSafeAreaConfig("NONE")}
      contentContainerStyle={$styles.flex1}
      statusBarStyle="light"
      backgroundColor={colors.background}
    >
      {/* Combined Header Wrapper */}
      <View style={themed($headerWrapper)}>
        {/* Profile Header */}
        <ProfileHeader
          profile={profileData.profile}
          stats={profileData.stats}
          activeTab={activeTab}
          onTabChange={handleTabChange}
          isCurrentUser={isCurrentUser}
          onMessagePress={handleMessagePress}
        />
      </View>

      {/* Profile Content */}
      {/* Profile Content */}
      <ProfileList
        ref={scrollViewRef}
        activeTab={activeTab}
        upcomingEvents={profileData.upcomingEvents}
        pastEvents={profileData.pastEvents}
        communities={profileData.communities}
        trophies={profileData.trophies}
        onEventPress={handleEventPress}
        onCommunityPress={handleCommunityPress}
        onEventDetailPress={handleEventDetailPress}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={refreshProfileData} />
        }
      />

      {/* Settings Popup */}
      <SettingsPopup
        visible={settingsPopupVisible}
        onClose={handleCloseSettingsPopup}
        onEditProfile={handleEditProfile}
        onChangePassword={handleChangePassword}
        onHelp={handleHelp}
        lightMode={themeContext === "light"}
        onToggleLightMode={handleToggleLightMode}
        position={{
          top: Platform.OS === 'ios' ? 100 : rightButtonsLayout.y + rightButtonsLayout.height + 8, // Hardcoded top for iOS, dynamic for Android
          right: 20, // Keep it aligned to the right
        }}
      />
    </Screen>
  )
})

// Export the scroll function for use in navigator
export const triggerProfileScreenScrollToTop = () => {
  scrollManager.triggerScrollToTop()
}

const $headerWrapper: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingBottom: spacing.sm,
})
