import { useState, useCallback, useEffect } from "react"
import { getUserData, isCurrentUser } from "../utils"
import { useStores } from "@/models"
import { SupabaseProfileService } from "@/services/supabase/profile-service"
import { ProfileData } from "../types"
import { Alert } from "react-native"

interface UseProfileDataProps {
  userId?: string
}

export const useProfileData = ({ userId }: UseProfileDataProps) => {
  const { userProfileStore } = useStores()
  const [profileData, setProfileData] = useState<ProfileData>(() => {
    const initialData = getUserData(userId)
    if (isCurrentUser(userId)) {
      initialData.profile.name = userProfileStore.displayName || initialData.profile.name
      initialData.profile.description = userProfileStore.description || initialData.profile.description
      initialData.stats = {
        totalMatches: userProfileStore.totalMatches,
        socialWins: userProfileStore.socialWins,
        tournamentWins: userProfileStore.tournamentWins,
      }
      initialData.trophies = userProfileStore.trophies.slice()
      initialData.communities = userProfileStore.communities.slice()
    }
    return initialData
  })
  const [isRefreshing, setIsRefreshing] = useState(false)

  const fetchAndCacheProfileData = useCallback(async () => {
    if (!isCurrentUser(userId)) return // Only fetch for the current user for now

    try {
      const [profile, stats, trophies, communities] = await Promise.all([
        SupabaseProfileService.fetchCurrentUserProfile(),
        SupabaseProfileService.fetchStats(),
        SupabaseProfileService.fetchTrophies(),
        SupabaseProfileService.fetchUserCommunities(),
      ])

      if (profile) {
        userProfileStore.setProfile({
          displayName: profile.name,
          email: profile.email,
          countryCode: profile.countryCode,
          phone: profile.phone,
          gender: profile.gender,
          dateOfBirth: profile.dateOfBirth,
          description: profile.description,
          location: profile.location,
        })
      }
      if (stats) userProfileStore.setStats(stats)
      if (trophies) userProfileStore.setTrophies(trophies)
      if (communities) userProfileStore.setCommunities(communities)

      // After updating the store, rebuild the profileData state
      const updatedData = getUserData(userId)
      updatedData.profile.name = userProfileStore.displayName || updatedData.profile.name
      updatedData.profile.description = userProfileStore.description || updatedData.profile.description
      updatedData.stats = {
        totalMatches: userProfileStore.totalMatches,
        socialWins: userProfileStore.socialWins,
        tournamentWins: userProfileStore.tournamentWins,
      }
      updatedData.trophies = userProfileStore.trophies.slice()
      updatedData.communities = userProfileStore.communities.slice()
      setProfileData(updatedData)
    } catch (error) {
      console.error("Failed to fetch profile data:", error)
      Alert.alert("Error", "Could not update profile data.")
    }
  }, [userId, userProfileStore])

  const refreshProfileData = useCallback(async () => {
    setIsRefreshing(true)
    await fetchAndCacheProfileData()
    setIsRefreshing(false)
  }, [fetchAndCacheProfileData])

  useEffect(() => {
    // Initial fetch when the component mounts
    fetchAndCacheProfileData()
  }, [fetchAndCacheProfileData])

  return {
    profileData,
    isCurrentUser: isCurrentUser(userId),
    isRefreshing,
    refreshProfileData,
  }
}
